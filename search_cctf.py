#!/usr/bin/env python3

from Crypto.Util.number import long_to_bytes
from decimal import Decimal

# My best estimate of x0
x0 = Decimal('0.699239966278438130967830082246505186510990321789278382093105501294350990985053460893651545442955915203656438618816249687416120965675969287071405758826893766130878806366754986365490212127805200121806516646977996268590698821412133075700607058611423170307267262717995527415259344649889072022058768933665672404747303827097170379756274627425227663479745388031005859375')

print(f"x0: {x0}")

# Try a much wider range and look for any occurrence of CCTF in any case
print("Searching for CCTF patterns...")

found_anything = False

for length in range(1, 300):
    try:
        scaled = x0 * (Decimal(10) ** length)
        m_candidate = int(scaled)
        
        if len(str(m_candidate)) == length:
            flag_bytes = long_to_bytes(m_candidate)
            
            # Convert to string with different error handling
            for errors in ['ignore', 'replace']:
                try:
                    flag_str = flag_bytes.decode('utf-8', errors=errors)
                    
                    # Look for CCTF in any case
                    flag_upper = flag_str.upper()
                    if 'CCTF' in flag_upper:
                        print(f"Length {length} (errors={errors}): {repr(flag_str)}")
                        print(f"  Upper case: {flag_upper}")
                        found_anything = True
                        
                        # Look for flag-like patterns
                        if '{' in flag_str and '}' in flag_str:
                            print(f"  *** HAS BRACES - POTENTIAL FLAG: {flag_str} ***")
                            
                except:
                    pass
                    
            # Also try latin-1 encoding
            try:
                flag_str = flag_bytes.decode('latin-1')
                flag_upper = flag_str.upper()
                if 'CCTF' in flag_upper:
                    print(f"Length {length} (latin-1): {repr(flag_str)}")
                    found_anything = True
            except:
                pass
                
    except:
        pass

if not found_anything:
    print("No CCTF patterns found. Let me try a different approach...")
    
    # Try interpreting the digits directly
    x0_str = str(x0)
    if x0_str.startswith('0.'):
        digits = x0_str[2:]
    else:
        digits = x0_str
    
    print(f"Trying digit interpretation on: {digits[:100]}...")
    
    # Look for patterns in the raw digits that might correspond to ASCII values
    for start in range(min(50, len(digits))):
        for length in range(30, min(len(digits) - start + 1, 150)):
            try:
                candidate_str = digits[start:start+length]
                if not candidate_str:
                    continue
                    
                m_candidate = int(candidate_str)
                flag_bytes = long_to_bytes(m_candidate)
                
                # Try different encodings
                for encoding in ['utf-8', 'latin-1']:
                    for errors in ['ignore', 'replace']:
                        try:
                            flag_str = flag_bytes.decode(encoding, errors=errors)
                            flag_upper = flag_str.upper()
                            
                            if 'CCTF' in flag_upper:
                                print(f"Digits [{start}:{start+length}] ({encoding}, {errors}): {repr(flag_str)}")
                                if '{' in flag_str and '}' in flag_str:
                                    print(f"  *** POTENTIAL FLAG: {flag_str} ***")
                                    
                        except:
                            pass
            except:
                pass

print("Search completed.")
