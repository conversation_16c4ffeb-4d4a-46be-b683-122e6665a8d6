#!/usr/bin/env python3

from Crypto.Util.number import long_to_bytes
from decimal import Decimal

# The most precise x0 I found
x0 = Decimal('0.7120078592701129246675410680918314018030278429771030335496321722804339323602920948534072953735276000846220420589233344899189781066290131437323204734540926729868467329159831432853394818564710797007904543981469481330481132068989395256599521284081613127065686332173988889059305838408898085583582178094272975550826019641778179006224451324823808954984063579559876563692749274002319863471945191421983741957580002001993304535129358352950548642935793684039341905256607122281310846247060533218893093120041690764304777277002534923489610610217019386525184499118785245181789345094328360672704546674612047230112921170075424015522003173828125')

print(f"x0: {x0}")

# Convert to string and examine the digits
x0_str = str(x0)
if x0_str.startswith('0.'):
    digits = x0_str[2:]
else:
    digits = x0_str

print(f"Digits: {digits}")
print(f"Length: {len(digits)}")

# Let me try a completely exhaustive search
print("\nExhaustive search for any text containing letters...")

results = []

# Try every possible length from 1 to 200
for length in range(1, 201):
    try:
        scaled = x0 * (Decimal(10) ** length)
        m_candidate = int(scaled)
        
        if len(str(m_candidate)) == length:
            flag_bytes = long_to_bytes(m_candidate)
            
            # Try to decode and look for any readable text
            for encoding in ['utf-8', 'latin-1', 'ascii']:
                for errors in [None, 'ignore', 'replace']:
                    try:
                        if errors is None:
                            flag_str = flag_bytes.decode(encoding)
                        else:
                            flag_str = flag_bytes.decode(encoding, errors=errors)
                        
                        # Look for any string with at least 4 letters
                        letter_count = sum(1 for c in flag_str if c.isalpha())
                        if letter_count >= 4:
                            results.append((length, encoding, errors, flag_str))
                            
                    except:
                        pass
    except:
        pass

# Sort results by length and show the most promising ones
results.sort(key=lambda x: x[0])

print(f"\nFound {len(results)} results with 4+ letters:")
for length, encoding, errors, flag_str in results[:50]:  # Show first 50
    print(f"Length {length} ({encoding}, {errors}): {repr(flag_str)}")
    
    # Highlight anything that looks like a flag
    upper_str = flag_str.upper()
    if 'CCTF' in upper_str or 'CTF' in upper_str or ('{' in flag_str and '}' in flag_str):
        print(f"  *** INTERESTING: {flag_str} ***")

# Also try direct digit interpretation
print(f"\nTrying direct digit interpretation...")
for start in range(min(100, len(digits))):
    for length in range(40, min(len(digits) - start + 1, 120)):
        try:
            candidate_str = digits[start:start+length]
            if not candidate_str or candidate_str[0] == '0':
                continue
                
            m_candidate = int(candidate_str)
            flag_bytes = long_to_bytes(m_candidate)
            
            for encoding in ['utf-8', 'latin-1']:
                for errors in ['ignore', 'replace']:
                    try:
                        flag_str = flag_bytes.decode(encoding, errors=errors)
                        
                        # Look for flag-like patterns
                        if 'CCTF' in flag_str.upper() or ('CTF' in flag_str.upper() and '{' in flag_str):
                            print(f"Digits [{start}:{start+length}] ({encoding}, {errors}): {repr(flag_str)}")
                            
                    except:
                        pass
        except:
            pass

print("Manual decode completed.")
