#!/usr/bin/env python3

import socket
import re
from decimal import Decimal, getcontext
from Crypto.Util.number import long_to_bytes

# Set even higher precision
getcontext().prec = 1000

def connect_to_server():
    """Connect to the CTF server"""
    host = "************"
    port = 11117
    
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.connect((host, port))
    return s

def rat_iteration(x, y):
    """Single iteration of the rat function - exactly matching the original code"""
    # From original: x = R(x + y) * R(0.5)
    # From original: y = R((x * y) ** 0.5)
    x_new = (x + y) * Decimal('0.5')
    y_new = (x * y).sqrt()
    return x_new, y_new

def agm_limit(x, y, iterations=100):
    """Calculate the arithmetic-geometric mean limit with more iterations"""
    for i in range(iterations):
        x_new, y_new = rat_iteration(x, y)
        if abs(x_new - y_new) < Decimal('1e-200'):
            break
        x, y = x_new, y_new
    return y

def solve_challenge():
    """Solve the Vainrat challenge with maximum precision"""
    s = connect_to_server()
    
    # Read initial output and wait for y0
    data = ""
    while "We know y0" not in data:
        chunk = s.recv(4096).decode()
        data += chunk
    
    # Extract y0 value with maximum precision
    y0_match = re.search(r'We know y0 = ([0-9.]+)', data)
    if not y0_match:
        print("Could not find y0 value!")
        return
    
    y0 = Decimal(y0_match.group(1))
    print(f"y0: {y0}")
    
    # Catch the rat until we get the final y value
    iteration_count = 0
    while True:
        s.send(b'c\n')
        response = s.recv(4096).decode()
        
        y_match = re.search(r'y = ([0-9.]+)', response)
        if y_match:
            final_y = Decimal(y_match.group(1))
            print(f"final_y: {final_y}")
            break
        
        iteration_count += 1
        if iteration_count > 25:
            print("Too many iterations")
            break
    
    s.close()
    
    # Use a more precise binary search
    low = Decimal('0')
    high = y0
    
    print("Starting high-precision binary search...")
    
    for iteration in range(5000):  # Many more iterations
        mid = (low + high) / 2
        agm_result = agm_limit(mid, y0, iterations=200)
        diff = abs(agm_result - final_y)
        
        if iteration % 500 == 0:
            print(f"Iteration {iteration}, diff: {diff}")
        
        if diff < Decimal('1e-150'):  # Very tight tolerance
            print(f"Found precise x0 with diff {diff}: {mid}")
            
            # Try to decode with this precise x0
            print("Trying to decode flag...")
            for length in range(30, 100):
                try:
                    scaled = mid * (Decimal(10) ** length)
                    m_candidate = int(scaled)
                    
                    if len(str(m_candidate)) == length:
                        flag_bytes = long_to_bytes(m_candidate)
                        
                        # Try multiple decoding approaches
                        for encoding in ['utf-8', 'latin-1']:
                            for errors in [None, 'ignore', 'replace']:
                                try:
                                    if errors is None:
                                        flag_str = flag_bytes.decode(encoding)
                                    else:
                                        flag_str = flag_bytes.decode(encoding, errors=errors)
                                    
                                    # Look for flag patterns
                                    if 'CCTF' in flag_str.upper():
                                        print(f"Length {length} ({encoding}, {errors}): {repr(flag_str)}")
                                        if '{' in flag_str and '}' in flag_str:
                                            print(f"*** COMPLETE FLAG: {flag_str} ***")
                                            return flag_str
                                            
                                except:
                                    pass
                except:
                    pass
            break
        
        if agm_result < final_y:
            low = mid
        else:
            high = mid
    
    print("Could not find flag")
    return None

if __name__ == "__main__":
    solve_challenge()
