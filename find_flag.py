#!/usr/bin/env python3

from Crypto.Util.number import long_to_bytes
from decimal import Decimal

# This is the best x0 I found from the server
x0 = Decimal('0.7526977603353410064164813998965014684832710717863397175381604351459240321608409505860804657233686032666973474206341639942782007295120071564757890335773251065774172575699269754283672689943542172806757241363843353803333962217199480326421775073774879032358414059373309912766772221233392323422418355527463019775713273277974353636654569754682597704231739044189453125')

print(f"x0: {x0}")

# Try the standard mathematical approach with a wide range of lengths
print("Trying standard mathematical approach:")
for length in range(1, 150):
    try:
        scaled = x0 * (Decimal(10) ** length)
        m_candidate = int(scaled)
        
        if len(str(m_candidate)) == length:
            flag_bytes = long_to_bytes(m_candidate)
            
            # Try both strict and lenient decoding
            for errors in [None, 'ignore', 'replace']:
                try:
                    if errors is None:
                        flag_str = flag_bytes.decode('utf-8')
                    else:
                        flag_str = flag_bytes.decode('utf-8', errors=errors)
                    
                    # Look for any occurrence of CCTF
                    if 'CCTF' in flag_str:
                        print(f"Length {length} (errors={errors}): {repr(flag_str)}")
                        
                        # If it looks like a complete flag, highlight it
                        if flag_str.startswith('CCTF{') and '}' in flag_str:
                            print(f"*** POTENTIAL COMPLETE FLAG: {flag_str} ***")
                            
                except:
                    pass
    except:
        pass

print("\nAlso trying direct digit interpretation:")
# Convert to string and extract digits
x0_str = str(x0)
if x0_str.startswith('0.'):
    digits = x0_str[2:]
else:
    digits = x0_str

# Try interpreting substrings of digits directly
for start in range(min(20, len(digits))):
    for length in range(30, min(len(digits) - start + 1, 100)):
        try:
            candidate_str = digits[start:start+length]
            if not candidate_str:
                continue
                
            m_candidate = int(candidate_str)
            flag_bytes = long_to_bytes(m_candidate)
            
            for errors in [None, 'ignore', 'replace']:
                try:
                    if errors is None:
                        flag_str = flag_bytes.decode('utf-8')
                    else:
                        flag_str = flag_bytes.decode('utf-8', errors=errors)
                    
                    if 'CCTF' in flag_str:
                        print(f"Digits [{start}:{start+length}] (errors={errors}): {repr(flag_str)}")
                        
                        if flag_str.startswith('CCTF{') and '}' in flag_str:
                            print(f"*** POTENTIAL COMPLETE FLAG: {flag_str} ***")
                            
                except:
                    pass
        except:
            pass
