#!/usr/bin/env python3

from Crypto.Util.number import *
from decimal import Decimal, getcontext

# Set high precision
getcontext().prec = 500

# Test the conversion logic from the original code
test_flag = b"CCTF{test_flag_123}"
m = bytes_to_long(test_flag)
print(f"Original flag: {test_flag}")
print(f"Flag as number m: {m}")
print(f"Length of str(m): {len(str(m))}")

# This is how x0 is calculated in the original code
x0 = Decimal(10 ** (-len(str(m))) * m)
print(f"x0: {x0}")

# Now let's try to reverse it
# We know: x0 = 10^(-len(str(m))) * m
# So: m = x0 * 10^(len(str(m)))

# The challenge is we don't know len(str(m)), so we need to try different lengths
for length in range(1, 100):
    m_candidate = int(x0 * (Decimal(10) ** length))
    if len(str(m_candidate)) == length:
        print(f"Length {length}: m_candidate = {m_candidate}")
        try:
            recovered_flag = long_to_bytes(m_candidate)
            print(f"Recovered flag: {recovered_flag}")
            if recovered_flag == test_flag:
                print("SUCCESS! Conversion works correctly.")
                break
        except:
            pass
