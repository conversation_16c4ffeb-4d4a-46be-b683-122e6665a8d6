#!/usr/bin/env python3

from decimal import Decimal, getcontext
from Crypto.Util.number import *

# Set high precision
getcontext().prec = 500

def rat_iteration(x, y):
    """Single iteration of the rat function - matches the original code"""
    x_new = (x + y) * Decimal('0.5')  # This matches the original: R(x + y) * R(0.5)
    y_new = (x * y).sqrt()            # This matches the original: R((x * y) ** 0.5)
    return x_new, y_new

def agm_limit(x, y, iterations=50):
    """Calculate the arithmetic-geometric mean limit"""
    for i in range(iterations):
        x_new, y_new = rat_iteration(x, y)
        if abs(x_new - y_new) < Decimal('1e-150'):
            print(f"Converged after {i+1} iterations")
            break
        x, y = x_new, y_new
    return y

# Test with a known flag
test_flag = b"CCTF{test_flag_123}"
m = bytes_to_long(test_flag)
print(f"Test flag: {test_flag}")
print(f"m: {m}")
print(f"len(str(m)): {len(str(m))}")

# Calculate x0 as in the original code
x0 = Decimal(10 ** (-len(str(m))) * m)
print(f"x0: {x0}")

# Generate a random y0 > x0 (simulate the original)
y0 = x0 + Decimal('0.1')  # Simple test case
print(f"y0: {y0}")

# Run the AGM iterations
final_y = agm_limit(x0, y0)
print(f"final_y: {final_y}")

# Now try to recover x0 from y0 and final_y
print("\nTrying to recover x0...")

# Binary search
low = Decimal('0')
high = y0

for iteration in range(1000):
    mid = (low + high) / 2
    agm_result = agm_limit(mid, y0)
    diff = abs(agm_result - final_y)
    
    if diff < Decimal('1e-100'):
        print(f"Found x0: {mid}")
        print(f"Original x0: {x0}")
        print(f"Difference: {abs(mid - x0)}")
        
        # Try to recover the flag
        for length in range(1, 100):
            try:
                scaled = mid * (Decimal(10) ** length)
                m_candidate = int(scaled)
                
                if len(str(m_candidate)) == length:
                    recovered_flag = long_to_bytes(m_candidate)
                    if recovered_flag == test_flag:
                        print(f"SUCCESS! Recovered flag: {recovered_flag}")
                        break
            except:
                pass
        break
    
    if agm_result < final_y:
        low = mid
    else:
        high = mid
    
    if iteration % 100 == 0:
        print(f"Iteration {iteration}, diff: {diff}")

print("Test completed.")
