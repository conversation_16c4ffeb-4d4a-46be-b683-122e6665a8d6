#!/usr/bin/env python3

from Crypto.Util.number import long_to_bytes
from decimal import Decimal

# My best estimate of x0
base_x0 = Decimal('0.7526977603353410064164813998965014684832710717863397175381604351459240321608409505860804657233686032666973474206341639942782007295120071564757890335773251065774172575699269754283672689943542172806757241363843353803333962217199480326421775073774879032358414059373309912766772221233392323422418355527463019775713273277974353636654569754682597704231739044189453125')

print(f"Base x0: {base_x0}")

# Try a range of x0 values around my estimate
delta = Decimal('0.001')  # Try values within ±0.001 of my estimate
step = Decimal('0.0001')  # Step size

current = base_x0 - delta
while current <= base_x0 + delta:
    # Try the standard mathematical approach for this x0
    for length in range(40, 80):  # Focus on reasonable flag lengths
        try:
            scaled = current * (Decimal(10) ** length)
            m_candidate = int(scaled)
            
            if len(str(m_candidate)) == length:
                flag_bytes = long_to_bytes(m_candidate)
                
                # Try both strict and lenient decoding
                for errors in [None, 'ignore']:
                    try:
                        if errors is None:
                            flag_str = flag_bytes.decode('utf-8')
                        else:
                            flag_str = flag_bytes.decode('utf-8', errors=errors)
                        
                        # Look for any occurrence of CCTF
                        if 'CCTF{' in flag_str:
                            print(f"x0={current}, Length {length} (errors={errors}): {repr(flag_str)}")
                            
                            # If it looks like a complete flag, highlight it
                            if '}' in flag_str:
                                print(f"*** POTENTIAL COMPLETE FLAG: {flag_str} ***")
                                
                    except:
                        pass
        except:
            pass
    
    current += step

print("Range search completed.")
