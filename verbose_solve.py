#!/usr/bin/env python3

import socket
import re
from decimal import Decimal, getcontext
from Crypto.Util.number import long_to_bytes

# Set high precision for decimal calculations
getcontext().prec = 500

def connect_to_server():
    """Connect to the CTF server"""
    host = "************"
    port = 11117
    
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.connect((host, port))
    return s

def rat_iteration(x, y):
    """Single iteration of the rat function - matches the original code"""
    x_new = (x + y) * Decimal('0.5')
    y_new = (x * y).sqrt()
    return x_new, y_new

def agm_limit(x, y, iterations=50):
    """Calculate the arithmetic-geometric mean limit"""
    for _ in range(iterations):
        x_new, y_new = rat_iteration(x, y)
        if abs(x_new - y_new) < Decimal('1e-150'):
            break
        x, y = x_new, y_new
    return y

def solve_challenge():
    """Solve the Vainrat challenge"""
    s = connect_to_server()
    
    # Read initial output and wait for y0
    data = ""
    while "We know y0" not in data:
        chunk = s.recv(4096).decode()
        data += chunk
    
    # Extract y0 value
    y0_match = re.search(r'We know y0 = ([0-9.]+)', data)
    if not y0_match:
        print("Could not find y0 value!")
        return
    
    y0 = Decimal(y0_match.group(1))
    print(f"Extracted y0: {y0}")
    
    # Catch the rat until we get the final y value
    iteration_count = 0
    while True:
        s.send(b'c\n')
        response = s.recv(4096).decode()
        
        y_match = re.search(r'y = ([0-9.]+)', response)
        if y_match:
            final_y = Decimal(y_match.group(1))
            print(f"Got final y value: {final_y}")
            break
        
        iteration_count += 1
        if iteration_count > 25:
            print("Too many iterations")
            break
    
    s.close()
    
    # Binary search for x0
    low = Decimal('0')
    high = y0
    
    best_x0 = None
    best_diff = float('inf')
    
    for iteration in range(2000):
        mid = (low + high) / 2
        agm_result = agm_limit(mid, y0)
        diff = abs(agm_result - final_y)
        
        if diff < best_diff:
            best_diff = diff
            best_x0 = mid
        
        if diff < Decimal('1e-70'):
            break
        
        if agm_result < final_y:
            low = mid
        else:
            high = mid
    
    print(f"Best x0 found: {best_x0}")
    
    # Now try EVERY possible length and show ALL results that contain any printable characters
    print("\nTrying all possible flag decodings:")
    for length in range(1, 200):
        try:
            scaled = best_x0 * (Decimal(10) ** length)
            m_candidate = int(scaled)
            
            if len(str(m_candidate)) == length:
                flag_bytes = long_to_bytes(m_candidate)
                
                # Try to decode and show anything that looks remotely like text
                try:
                    flag_str = flag_bytes.decode('utf-8')
                    # Show if it contains any letters or common flag characters
                    if any(c.isalpha() or c in '{}[]()_-' for c in flag_str):
                        print(f"Length {length}: {repr(flag_str)}")
                        if 'CCTF' in flag_str or 'cctf' in flag_str:
                            print(f"  *** CONTAINS CCTF: {flag_str} ***")
                except:
                    try:
                        flag_str = flag_bytes.decode('utf-8', errors='ignore')
                        if any(c.isalpha() or c in '{}[]()_-' for c in flag_str):
                            print(f"Length {length} (ignore errors): {repr(flag_str)}")
                            if 'CCTF' in flag_str or 'cctf' in flag_str:
                                print(f"  *** CONTAINS CCTF: {flag_str} ***")
                    except:
                        pass
        except:
            pass

if __name__ == "__main__":
    solve_challenge()
