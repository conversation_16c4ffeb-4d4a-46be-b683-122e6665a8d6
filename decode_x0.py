#!/usr/bin/env python3

from Crypto.Util.number import *
from decimal import Decimal, getcontext

# Set high precision
getcontext().prec = 500

# This is the x0 value I found from the previous run
x0 = Decimal('0.7223984700398712338769203997643238547862257870072054219213110178434395178185807728012358473341420701088518314108615259982793928163246070734396709929912102262561848574389200680808883147157997922275601938481775077189634219497203374473826030276846700980725751548908761201194763271267734433146446456131119623138561102577107440922846386383640635610418821960058977094121246409486047923564910888671875')

print(f"x0: {x0}")

# Try different approaches to decode this
x0_str = str(x0)
print(f"x0 as string: {x0_str}")

# Remove the "0." prefix
if x0_str.startswith('0.'):
    digits = x0_str[2:]
else:
    digits = x0_str

print(f"Digits: {digits}")
print(f"Number of digits: {len(digits)}")

# Try the standard approach: x0 = 10^(-len(str(m))) * m
# So m = x0 * 10^(len(str(m)))
print("\nTrying standard approach:")
for length in range(1, 200):
    try:
        scaled = x0 * (Decimal(10) ** length)
        m_candidate = int(scaled)
        
        if len(str(m_candidate)) == length:
            try:
                flag_bytes = long_to_bytes(m_candidate)
                flag_str = flag_bytes.decode('utf-8', errors='ignore')
                
                if 'CCTF' in flag_str:
                    print(f"Length {length}: {flag_str}")
                    if '}' in flag_str:
                        print(f"COMPLETE FLAG: {flag_str}")
                        break
            except:
                pass
    except:
        pass

# Try interpreting the digits directly as a number
print("\nTrying direct digit interpretation:")
for start in range(min(5, len(digits))):
    for length in range(20, min(len(digits) - start + 1, 100)):
        try:
            candidate_str = digits[start:start+length]
            if not candidate_str:
                continue
                
            m_candidate = int(candidate_str)
            flag_bytes = long_to_bytes(m_candidate)
            flag_str = flag_bytes.decode('utf-8', errors='ignore')
            
            if 'CCTF' in flag_str:
                print(f"Start {start}, Length {length}: {flag_str}")
                if '}' in flag_str:
                    print(f"COMPLETE FLAG: {flag_str}")
        except:
            pass

# Try different scaling factors
print("\nTrying different scaling factors:")
for scale in [10, 100, 1000, 10000, 100000]:
    try:
        scaled = x0 * scale
        m_candidate = int(scaled)
        flag_bytes = long_to_bytes(m_candidate)
        flag_str = flag_bytes.decode('utf-8', errors='ignore')
        
        if 'CCTF' in flag_str:
            print(f"Scale {scale}: {flag_str}")
    except:
        pass
