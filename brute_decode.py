#!/usr/bin/env python3

from Crypto.Util.number import long_to_bytes
from decimal import Decimal

# This is the best x0 I found
x0 = Decimal('0.705037674119343198989165061491508172397600033339984404238145980267055186466043417791210066645532903071399825709793591758308072510530406168554492705548580010810052223267910750746781522984599440298975381427203926578925655553164116819372774272194475132409033509223333803948386608857419163412474333578428484547506021405780148036956944679332082159817218780517578125')

print(f"x0: {x0}")

# Convert to string and extract digits
x0_str = str(x0)
if x0_str.startswith('0.'):
    digits = x0_str[2:]
else:
    digits = x0_str

print(f"Digits: {digits}")
print(f"Length: {len(digits)}")

# Try every possible substring that could be a flag
found_flags = []

for start in range(len(digits)):
    for end in range(start + 20, min(len(digits) + 1, start + 200)):
        try:
            candidate_str = digits[start:end]
            if not candidate_str or candidate_str[0] == '0':
                continue
                
            m_candidate = int(candidate_str)
            if m_candidate < 256**10:  # Skip very small numbers
                continue
                
            flag_bytes = long_to_bytes(m_candidate)
            
            # Try to decode as UTF-8
            try:
                flag_str = flag_bytes.decode('utf-8')
                if 'CCTF' in flag_str:
                    print(f"Found potential flag at [{start}:{end}]: {flag_str}")
                    if '{' in flag_str and '}' in flag_str:
                        found_flags.append(flag_str)
            except:
                pass
                
            # Also try to decode with errors='ignore'
            try:
                flag_str = flag_bytes.decode('utf-8', errors='ignore')
                if 'CCTF{' in flag_str and '}' in flag_str and len(flag_str) > 10:
                    if flag_str not in found_flags:
                        print(f"Found potential flag (ignore errors) at [{start}:{end}]: {flag_str}")
                        found_flags.append(flag_str)
            except:
                pass
                
        except Exception as e:
            pass

print(f"\nFound {len(found_flags)} potential flags:")
for flag in found_flags:
    print(f"  {flag}")

# Also try the standard mathematical approach with more precision
print("\nTrying standard mathematical approach:")
for length in range(40, 80):  # Focus on reasonable flag lengths
    try:
        scaled = x0 * (Decimal(10) ** length)
        m_candidate = int(scaled)
        
        if len(str(m_candidate)) == length:
            flag_bytes = long_to_bytes(m_candidate)
            try:
                flag_str = flag_bytes.decode('utf-8')
                if 'CCTF' in flag_str:
                    print(f"Mathematical approach (length {length}): {flag_str}")
            except:
                try:
                    flag_str = flag_bytes.decode('utf-8', errors='ignore')
                    if 'CCTF' in flag_str:
                        print(f"Mathematical approach with errors (length {length}): {flag_str}")
                except:
                    pass
    except:
        pass
