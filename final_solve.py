#!/usr/bin/env python3

import socket
import re
from decimal import Decimal, getcontext
from Crypto.Util.number import long_to_bytes

# Set high precision for decimal calculations
getcontext().prec = 500

def connect_to_server():
    """Connect to the CTF server"""
    host = "************"
    port = 11117
    
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.connect((host, port))
    return s

def rat_iteration(x, y):
    """Single iteration of the rat function - matches the original code"""
    x_new = (x + y) * Decimal('0.5')
    y_new = (x * y).sqrt()
    return x_new, y_new

def agm_limit(x, y, iterations=50):
    """Calculate the arithmetic-geometric mean limit"""
    for _ in range(iterations):
        x_new, y_new = rat_iteration(x, y)
        if abs(x_new - y_new) < Decimal('1e-150'):
            break
        x, y = x_new, y_new
    return y

def try_decode_x0(x0):
    """Try various ways to decode x0 into a flag"""
    print(f"Trying to decode x0: {x0}")
    
    # Method 1: Standard approach - x0 = 10^(-len(str(m))) * m
    for length in range(1, 300):
        try:
            scaled = x0 * (Decimal(10) ** length)
            m_candidate = int(scaled)
            
            if len(str(m_candidate)) == length:
                flag_bytes = long_to_bytes(m_candidate)
                flag_str = flag_bytes.decode('utf-8', errors='ignore')
                
                if 'CCTF{' in flag_str and '}' in flag_str:
                    return flag_str
                elif 'CCTF' in flag_str:
                    print(f"Partial flag (length {length}): {flag_str}")
        except:
            pass
    
    # Method 2: Try interpreting digits directly
    x0_str = str(x0)
    if x0_str.startswith('0.'):
        digits = x0_str[2:]
    else:
        digits = x0_str
    
    for start in range(min(10, len(digits))):
        for length in range(30, min(len(digits) - start + 1, 150)):
            try:
                candidate_str = digits[start:start+length]
                if not candidate_str:
                    continue
                    
                m_candidate = int(candidate_str)
                flag_bytes = long_to_bytes(m_candidate)
                flag_str = flag_bytes.decode('utf-8', errors='ignore')
                
                if 'CCTF{' in flag_str and '}' in flag_str:
                    return flag_str
                elif 'CCTF' in flag_str:
                    print(f"Partial flag (start {start}, length {length}): {flag_str}")
            except:
                pass
    
    # Method 3: Try different scaling factors
    for scale_exp in range(1, 200):
        try:
            scaled = x0 * (Decimal(10) ** scale_exp)
            m_candidate = int(scaled)
            flag_bytes = long_to_bytes(m_candidate)
            flag_str = flag_bytes.decode('utf-8', errors='ignore')
            
            if 'CCTF{' in flag_str and '}' in flag_str:
                return flag_str
        except:
            pass
    
    return None

def solve_challenge():
    """Solve the Vainrat challenge"""
    s = connect_to_server()
    
    # Read initial output and wait for y0
    data = ""
    while "We know y0" not in data:
        chunk = s.recv(4096).decode()
        data += chunk
    
    # Extract y0 value
    y0_match = re.search(r'We know y0 = ([0-9.]+)', data)
    if not y0_match:
        print("Could not find y0 value!")
        return
    
    y0 = Decimal(y0_match.group(1))
    print(f"Extracted y0: {y0}")
    
    # Catch the rat until we get the final y value
    iteration_count = 0
    while True:
        s.send(b'c\n')
        response = s.recv(4096).decode()
        
        y_match = re.search(r'y = ([0-9.]+)', response)
        if y_match:
            final_y = Decimal(y_match.group(1))
            print(f"Got final y value: {final_y}")
            break
        
        iteration_count += 1
        if iteration_count > 25:
            print("Too many iterations")
            break
    
    s.close()
    
    print(f"y0 = {y0}")
    print(f"final_y = {final_y}")
    
    # Binary search for x0
    low = Decimal('0')
    high = y0
    
    best_x0 = None
    best_diff = float('inf')
    
    for iteration in range(2000):  # More iterations
        mid = (low + high) / 2
        agm_result = agm_limit(mid, y0)
        diff = abs(agm_result - final_y)
        
        if diff < best_diff:
            best_diff = diff
            best_x0 = mid
        
        if diff < Decimal('1e-70'):  # Even more relaxed tolerance
            print(f"Found good x0 with diff {diff}")
            break
        
        if agm_result < final_y:
            low = mid
        else:
            high = mid
    
    if best_x0 is None:
        print("Could not find x0")
        return None
    
    print(f"Best x0 found (diff={best_diff}): {best_x0}")
    
    # Try to decode the flag
    flag = try_decode_x0(best_x0)
    if flag:
        print(f"FLAG: {flag}")
        return flag
    else:
        print("Could not decode flag")
        return None

if __name__ == "__main__":
    solve_challenge()
