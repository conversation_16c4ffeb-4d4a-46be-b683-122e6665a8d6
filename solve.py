#!/usr/bin/env python3

import socket
import re
from decimal import Decimal, getcontext
from Crypto.Util.number import long_to_bytes

# Set high precision for decimal calculations
getcontext().prec = 500

def connect_to_server():
    """Connect to the CTF server"""
    host = "************"
    port = 11117
    
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.connect((host, port))
    return s

def rat_iteration(x, y):
    """Single iteration of the rat function"""
    x_new = (x + y) / 2
    y_new = (x * y).sqrt()
    return x_new, y_new

def agm_limit(x, y, iterations=50):
    """Calculate the arithmetic-geometric mean limit"""
    for _ in range(iterations):
        x_new = (x + y) / 2
        y_new = (x * y).sqrt()
        if abs(x_new - y_new) < Decimal('1e-150'):
            break
        x, y = x_new, y_new
    return y

def solve_challenge():
    """Solve the Vainrat challenge"""
    s = connect_to_server()

    # Read initial output and wait for y0
    data = ""
    while "We know y0" not in data:
        chunk = s.recv(4096).decode()
        data += chunk
        print("Received chunk:", repr(chunk))

    print("Full data:")
    print(data)

    # Extract y0 value using regex
    y0_match = re.search(r'We know y0 = ([0-9.]+)', data)
    if not y0_match:
        print("Could not find y0 value!")
        print("Trying alternative regex patterns...")
        # Try different patterns
        patterns = [
            r'y0 = ([0-9.]+)',
            r'y0=([0-9.]+)',
            r'= ([0-9.]+)',
        ]
        for pattern in patterns:
            match = re.search(pattern, data)
            if match:
                print(f"Found with pattern {pattern}: {match.group(1)}")
                y0_match = match
                break

        if not y0_match:
            return

    y0_str = y0_match.group(1)
    y0 = Decimal(y0_str)
    print(f"Extracted y0: {y0}")

    # We need to catch the rat multiple times until we get the final y value
    iteration_count = 0
    while True:
        # Send 'c' to catch the rat
        s.send(b'c\n')

        # Read response
        response = s.recv(4096).decode()
        print(f"Response {iteration_count}: {response}")

        # Check if we got a y value (final result)
        y_match = re.search(r'y = ([0-9.]+)', response)
        if y_match:
            final_y = Decimal(y_match.group(1))
            print(f"Got final y value: {final_y}")
            break

        iteration_count += 1
        if iteration_count > 25:  # Safety limit
            print("Too many iterations, something might be wrong")
            break

    s.close()

    print(f"y0 = {y0}")
    print(f"final_y = {final_y}")

    # Now we need to find x0 such that AGM(x0, y0) = final_y
    # We can use binary search since AGM is monotonic in both arguments

    # Binary search for x0
    low = Decimal('0')
    high = y0

    best_x0 = None
    best_diff = float('inf')

    for iteration in range(1000):  # Binary search iterations
        mid = (low + high) / 2
        agm_result = agm_limit(mid, y0)
        diff = abs(agm_result - final_y)

        if diff < best_diff:
            best_diff = diff
            best_x0 = mid

        if diff < Decimal('1e-80'):  # Relaxed tolerance
            print(f"Found x0 with diff {diff}: {mid}")
            break

        if agm_result < final_y:
            low = mid
        else:
            high = mid

    if best_x0 is None:
        print("Could not find x0")
        return None

    print(f"Best x0 found (diff={best_diff}): {best_x0}")

    # Try different lengths to recover the flag
    for length in range(1, 300):
        try:
            # Scale up x0 and convert to integer
            scaled = best_x0 * (Decimal(10) ** length)
            m_candidate = int(scaled)

            # Check if the length matches our scaling (this is the key constraint)
            if len(str(m_candidate)) == length:
                flag_bytes = long_to_bytes(m_candidate)
                flag_str = flag_bytes.decode('utf-8', errors='ignore')

                # Check for flag patterns
                if 'CCTF{' in flag_str and '}' in flag_str:
                    print(f"FLAG FOUND (length {length}): {flag_str}")
                    return flag_str
                elif 'CCTF' in flag_str:
                    print(f"Partial flag (length {length}): {flag_str}")
        except Exception as e:
            pass

    print("Could not recover the flag")
    return None

if __name__ == "__main__":
    solve_challenge()
